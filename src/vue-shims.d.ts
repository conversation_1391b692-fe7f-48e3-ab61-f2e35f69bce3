/* eslint-disable @typescript-eslint/no-explicit-any */

// 支持 defineProps/defineEmits/defineExpose/withDefaults 类型推断
import { DefineComponent } from 'vue'
import type { NavItemConfig } from './router/dataNav'
import { DataManage } from '@/utils/dataManage'

interface Bfglob {
  //可补充
  currentPlatform?: string
  [key: string]: any
  gdevices: typeof DataManage
  gorgData: typeof DataManage
  gGroupCallContacts: typeof DataManage
  gSingleCallContacts: typeof DataManage
}

declare global {
  const defineProps: (typeof import('vue'))['defineProps']
  const defineEmits: (typeof import('vue'))['defineEmits']
  const defineExpose: (typeof import('vue'))['defineExpose']
  const withDefaults: (typeof import('vue'))['withDefaults']

  // 全局变量自定义类型，如有具体类型可补充
  interface Window {
    bfglob: Bfglob
    bfaccount: {
      system?: string
      username?: string
      password?: string
      sid?: string
      sessionId?: string
      len?: number
      // 其他属性
      [key: string]: any
    }
    bfLogsConfigure: (option: string | Record<string, any>) => void
  }

  const bfglob: Bfglob
}

// 支持 .vue 文件类型导入
declare module '*.vue' {
  const component: DefineComponent<Record<string, unknown>, Record<string, unknown>, any>
  export default component
}

// 扩展 RouteMeta 接口
declare module 'vue-router' {
  interface RouteMeta {
    // 是否固定路由标签
    affix?: boolean
    // 是否在菜单中隐藏
    hidden?: boolean
    // 导航项配置
    navItemConfig?: ComputedRef<NavItemConfig>
  }
}
