import { reactive, computed } from 'vue'
import { cloneDeep, debounce } from 'lodash'
import bfProcess from '@/utils/bfprocess'
import { getDynamicGroupOrgType } from '@/utils/bfutil'
import dbCmd from '@/modules/protocol/db.pb.cmd'

// 通话信息接口定义
export interface SpeakInfo {
  speaker: string
  target: string
  maxSpeakTime: number
  priority: number
  listenGroup: string[]
}

// 设备频道接口
interface DeviceChannel {
  no: number
  listenGroup?: string[]
}

// 设备接口
interface Device {
  channels?: DeviceChannel[]
  selfId?: string
  dmrId?: string
}

// 组织接口
interface _Organization {
  orgShortName?: string
  dmrId?: string
  rid?: string
  orgIsVirtual?: number
}

// 动态组成员接口
interface _DynamicGroupMember {
  isDeviceGroup: number
  groupRid?: string
  deviceRid?: string
}

// 通话状态枚举
export enum SpeakState {
  ENDED = 0, // 结束讲话
  SPEAKING = 1, // 自己在讲话
  LISTENING = 2, // 他人在讲话
}

// 动态组类型
const dynamicGroupTypes = getDynamicGroupOrgType()

// 创建响应式的通话信息状态
const speakInfo = reactive<SpeakInfo>({
  speaker: '',
  target: '',
  maxSpeakTime: 60,
  priority: 3,
  listenGroup: [],
})

// 其他相关状态
const speakState = reactive({
  current: SpeakState.ENDED,
  otherSpeaking: false,
  currentSpeaker: '',
  pendingTarget: '',
  tempListenGroup: '',
})

// 计算属性
const cmdAgentSpeakDevice = computed(() => {
  return bfglob.gdevices.getDataByIndex(speakInfo.speaker)
})

const cmdAgentExLisGroup = computed(() => {
  const skDevice = cmdAgentSpeakDevice.value as Device
  let lsGroup: string[] = []
  if (skDevice && skDevice.channels) {
    lsGroup = cloneDeep(skDevice.channels.find((item: DeviceChannel) => item.no === 1)?.listenGroup || [])
  }
  return lsGroup
})

const listenGroupInfo = computed(() => {
  const lsGroup = Array.from(new Set([...cmdAgentExLisGroup.value, ...speakInfo.listenGroup]))

  return lsGroup.map(dmrId => {
    return (bfglob.gorgData.getDataByIndex(dmrId) || bfglob.noPermOrgData.getDataByIndex(dmrId))?.orgShortName ?? ''
  })
})

const dynamicGroup = computed(() => {
  const dynamicGroup = bfglob.gorgData.getDataByIndex(speakInfo.target)
  return dynamicGroupTypes.includes(dynamicGroup?.orgIsVirtual) ? dynamicGroup : undefined
})

const dynamicGroupMemberInfo = computed(() => {
  const members = bfglob.gdynamicGroupDetail.getDataByGroupRid(dynamicGroup.value?.rid)
  return members.map((item: _DynamicGroupMember) => {
    // 根据数据类型，查找成员名称
    if (item.isDeviceGroup === 2) {
      return bfglob.gorgData.getDataMaybeNoPerm(item.groupRid)?.orgShortName ?? ''
    }
    return bfglob.gdevices.getDataMaybeNoPerm(item.deviceRid)?.selfId ?? ''
  })
})

const setupTarget = computed(() => {
  return speakState.pendingTarget || speakInfo.target
})

const speakDevice = computed(() => {
  const device = bfglob.gdevices.getDataByIndex(speakInfo.speaker)
  return device && device.selfId ? device.selfId : ''
})

const listenGroupName = computed(() => {
  const dmrId = speakInfo.listenGroup[0]
  const orgItem = bfglob.gorgData.getDataByIndex(dmrId)
  const len = speakInfo.listenGroup.length
  return orgItem ? `${orgItem.orgShortName} ${len > 1 ? '+' + (len - 1) : ''}` : ''
})

const targetName = computed(() => {
  const key = speakInfo.target

  // 判断是否为全呼目标
  if (key === bfglob.fullCallDmrId) {
    return '全呼' // 这里可以根据需要使用 i18n
  }

  const orgItem = bfglob.gorgData.getDataByIndex(speakInfo.target)
  if (orgItem) {
    return orgItem.orgShortName || orgItem.dmrId || ''
  } else {
    const device = bfglob.gdevices.getDataByIndex(key)
    if (device) {
      return device.selfId || device.dmrId || ''
    }
  }

  return ''
})

const callbackTargetName = computed(() => {
  const org = bfglob.gorgData.getDataByIndex(speakState.pendingTarget)
  if (org) {
    return org.orgShortName
  }
  const device = bfglob.gdevices.getDataByIndex(speakState.pendingTarget)
  if (device) {
    return device.selfId
  }

  return speakState.pendingTarget
})

// 初始化通话信息
function initSpeakInfo() {
  const info = bfglob.userInfo.setting.voipSpeakInfo
  let data

  // 如果没有设置通话目标，则以用户所在组为通话目标
  if (!info.target || !(bfglob.gorgData.getDataByIndex(info.target) || bfglob.gdevices.getDataByIndex(info.target))) {
    data = bfglob.gorgData.get(bfglob.userInfo.orgId)
    info.target = data?.dmrId || ''
  }
  // 如果没有设置接收组，则接收用户所在组的语音
  if (!info.listenGroup || info.listenGroup.length === 0) {
    data = data || bfglob.gorgData.get(bfglob.userInfo.orgId)
    info.listenGroup = data ? [data.dmrId] : []
  } else {
    // 需要过滤已经不存在的dmrId数据
    info.listenGroup = info.listenGroup.filter(item => !!bfglob.gorgData.getDataByIndex(item))
  }

  // 合并参数
  Object.assign(speakInfo, info)
}

// 设置通话目标
function setSpeakTarget(key: string) {
  // 先判断是否为全呼
  if (key === bfglob.fullCallDmrId) {
    speakInfo.target = key
    return
  }

  const orgItem = bfglob.gorgData.get(key)
  if (orgItem) {
    speakInfo.target = orgItem.dmrId || ''
  } else {
    const device = bfglob.gdevices.get(key)
    if (device) {
      speakInfo.target = device.dmrId || ''
    } else {
      speakInfo.target = ''
    }
  }
}

// 设置收听组
function setSpeakListenGroup(key: string) {
  const org = bfglob.gorgData.get(key)
  if (org && org.dmrId) {
    speakInfo.listenGroup.push(org.dmrId)
  }
}

// 比较收听组是否有变化
function compareListenGroupChanged(listenGroup: string[], oldListenGroup: string[]) {
  if (listenGroup.length !== oldListenGroup.length) {
    return true
  }
  for (let i = 0; i < listenGroup.length; i++) {
    const item = listenGroup[i]
    const hasItem = oldListenGroup.includes(item)
    if (!hasItem) {
      return true
    }
  }
  return false
}

// 更新用户通话信息设置
const updateUserVoipSpeakInfo = debounce(() => {
  const skInfo: Partial<SpeakInfo> = {}
  Object.assign(skInfo, speakInfo)
  skInfo.listenGroup = speakInfo.listenGroup.filter(item => !cmdAgentExLisGroup.value.includes(item))
  bfglob.userInfo.setting.voipSpeakInfo = skInfo
  bfglob.userInfo.setting.ispUdateVoipSpeakInfo = true
  bfProcess.updateUserSetting(JSON.stringify(bfglob.userInfo.setting), dbCmd.DB_USER_PUPDATE)
}, 500)

// 设置通话状态
function setSpeakState(state: SpeakState) {
  speakState.current = state
}

// 设置挂起目标
function setPendingTarget(speaker_dmr_id: string, speaker_target_dmr_id: string) {
  // 单呼自己，回呼目标需要设置为源目标
  if (speakInfo.speaker === speaker_target_dmr_id) {
    speakState.pendingTarget = speaker_dmr_id
  } else {
    speakState.pendingTarget = speaker_target_dmr_id
  }
}

// 清除挂起目标
function cleanPendingTarget() {
  speakState.pendingTarget = ''
}

// 恢复目标
function resumeTarget() {
  cleanPendingTarget()
  cleanTempListenGroup()
}

// 清除临时收听组
function cleanTempListenGroup() {
  speakState.tempListenGroup = ''
}

// 设置临时收听组
function setTempListenGroup() {
  // 跳过单呼目标
  const device = bfglob.gdevices.getDataByIndex(speakInfo.target)
  if (device) {
    return
  }
  // 组呼目标已存在收听组
  if (speakInfo.listenGroup.includes(speakInfo.target)) {
    return
  }

  speakState.tempListenGroup = speakInfo.target
}

// 导出状态和方法
export {
  speakInfo,
  speakState,
  cmdAgentSpeakDevice,
  cmdAgentExLisGroup,
  listenGroupInfo,
  dynamicGroup,
  dynamicGroupMemberInfo,
  setupTarget,
  speakDevice,
  listenGroupName,
  targetName,
  callbackTargetName,
  initSpeakInfo,
  setSpeakTarget,
  setSpeakListenGroup,
  compareListenGroupChanged,
  updateUserVoipSpeakInfo,
  setSpeakState,
  setPendingTarget,
  cleanPendingTarget,
  resumeTarget,
  cleanTempListenGroup,
  setTempListenGroup,
}
