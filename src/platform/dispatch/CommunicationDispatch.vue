<script setup lang="ts">
  import { useI18n } from 'vue-i18n'
  import { convertPxToRemWithUnit, calcScaleSize } from '@/utils/setRem'
  import { computed, onMounted } from 'vue'
  import { DispatchContactCardProps } from './CommunicationDispatch/common/DispatchContactCard.vue'
  import { DispatchRecentContactCardProps } from './CommunicationDispatch/common/DispatchRecentContactCard.vue'
  import { DispatchDynamicContactProps } from './CommunicationDispatch/common/DispatchDynamicContact.vue'
  import { initSpeakInfo } from '@/utils/speak'

  const { t } = useI18n()
  const leftAsideWidth = convertPxToRemWithUnit(calcScaleSize(282))
  // const rightAsideWidth = convertPxToRemWithUnit(calcScaleSize(350))

  // todo: 使用真实数据
  const commonContactsList = computed<Array<DispatchContactCardProps>>(() => [
    { type: 'sdcTerminal', dmrIDHex: '00032222', name: 'idd', parentOrg: 'test' },
    { type: 'sdcTerminal', dmrIDHex: '00032223', name: 'idd', parentOrg: 'test' },
    { type: 'sdcTerminal', dmrIDHex: '00032224', name: 'idd', parentOrg: 'test' },
    { type: 'group', dmrIDHex: '00032225', name: 'idd', parentOrg: 'test' },
    { type: 'fullCallContact', dmrIDHex: '00032226', name: 'idd', parentOrg: 'test' },
    { type: 'sdcTerminal', dmrIDHex: '00032227', name: 'idd', parentOrg: 'test' },
    { type: 'sdcTerminal', dmrIDHex: '00032228', name: 'idd', parentOrg: 'test' },
    { type: 'sdcTerminal', dmrIDHex: '00032229', name: 'idd', parentOrg: 'test' },
  ])
  const recentContactList = computed<Array<DispatchRecentContactCardProps>>(() => [
    {
      srcName: 'srcName',
      srcType: 'sdcTerminal',
      targetDmrIDHex: '00032226',
      targetName: 'targetName',
      targetType: 'sdcTerminal',
      isCommonContact: false,
      soundTime: '12:00:00',
      soundLen: 60,
    },
    {
      srcName: 'srcName',
      srcType: 'sdcTerminal',
      targetDmrIDHex: '00032227',
      targetName: 'targetName',
      targetType: 'group',
      isCommonContact: false,
      soundTime: '12:11:00',
      soundLen: 60,
    },
    {
      srcName: 'srcName',
      srcType: 'sdcTerminal',
      targetDmrIDHex: '00032228',
      targetName: 'targetName',
      targetType: 'group',
      isCommonContact: true,
      soundTime: '12:11:00',
      soundLen: 60,
    },
    {
      srcName: 'srcName',
      srcType: 'sdcTerminal',
      targetDmrIDHex: '00032229',
      targetName: 'targetName',
      targetType: 'group',
      isCommonContact: true,
      soundTime: '12:11:00',
      soundLen: 60,
    },
    {
      srcName: 'srcName',
      srcType: 'sdcTerminal',
      targetDmrIDHex: '00032230',
      targetName: 'targetName',
      targetType: 'group',
      isCommonContact: true,
      soundTime: '12:11:00',
      soundLen: 60,
    },
  ])

  const dynamicGroupList = computed<DispatchDynamicContactProps>(() => [
    { type: 'taskGroup', dmrIDHex: '00032222', name: 'idd', parentOrg: 'test' },
    { type: 'tempGroup', dmrIDHex: '00032223', name: 'idd', parentOrg: 'test' },
    { type: 'taskGroup', dmrIDHex: '00032224', name: 'idd', parentOrg: 'test' },
    { type: 'taskGroup', dmrIDHex: '00032225', name: 'idd', parentOrg: 'test' },
    { type: 'taskGroup', dmrIDHex: '00032226', name: 'idd', parentOrg: 'test' },
    { type: 'taskGroup', dmrIDHex: '00032227', name: 'idd', parentOrg: 'test' },
    { type: 'taskGroup', dmrIDHex: '00032228', name: 'idd', parentOrg: 'test' },
    { type: 'taskGroup', dmrIDHex: '00032229', name: 'idd', parentOrg: 'test' },
  ])

  // todo: handle events
  const handleLocate = (targetDmrId: string) => {
    console.log('Locating:', targetDmrId)
  }

  const handleCall = (targetDmrId: string) => {
    console.log('Calling:', targetDmrId)
  }

  const handleHangup = (targetDmrId: string) => {
    console.log('Hanging up:', targetDmrId)
  }

  const handleMessage = (targetDmrId: string) => {
    console.log('Messaging:', targetDmrId)
  }

  const handleSendCommand = (targetDmrId: string) => {
    console.log('Sending command to:', targetDmrId)
  }

  const handleSendMessage = (targetDmrId: string) => {
    console.log('Sending message to:', targetDmrId)
  }

  const handleAddToCommonContact = (targetDmrId: string) => {
    console.log('Adding to common contacts:', targetDmrId)
  }

  const handleRemoveFromCommonContact = (targetDmrId: string) => {
    console.log('Removing from common contacts:', targetDmrId)
  }

  onMounted(() => {
    // 等待所有必要数据加载完成后再初始化通话信息
    const initSpeakInfoWhenReady = async () => {
      try {
        // 等待用户设置加载完成
        if (!bfglob.userInfo?.setting?.voipSpeakInfo) {
          await new Promise<void>(resolve => {
            const handleUserSettingsUpdate = settings => {
              if (settings.voipSpeakInfo !== undefined) {
                bfglob.off('update_user_settings', handleUserSettingsUpdate)
                resolve()
              }
            }
            bfglob.on('update_user_settings', handleUserSettingsUpdate)
          })
        }

        // 等待组织数据加载完成
        if (bfglob.orgsIsLoaded) {
          await bfglob.orgsIsLoaded
        }

        initSpeakInfo()
      } catch (error) {
        console.error('初始化通话信息失败:', error)
        setTimeout(() => {
          initSpeakInfo()
        }, 1000)
      }
    }

    initSpeakInfoWhenReady()
  })

  const openCommonContactsDialog = () => {
    console.log('Opening common contacts dialog')
    // todo: openDialog()
  }

  const cleanRecentContacts = () => {
    console.log('Cleaning recent contacts')
    // todo: cleanRecentContacts()
  }

  const openNewDynamicGroupDialog = () => {
    console.log('Opening new dynamic group dialog')
    // todo: openDialog()
  }
</script>

<template>
  <el-container class="flex-auto max-h-[100vh_-_146.5px] overflow-y-auto !px-[40px] !pb-[47px] !p-0 text-white">
    <el-aside class="flex flex-col gap-[15px]" :width="leftAsideWidth">
      <PageHeader :title="t('dispatch.functionList.name')" />
      <DispatchFunctionList />
    </el-aside>
    <el-main class="px-[15px]! py-0! flex! h-full flex-col gap-[15px]">
      <el-row class="h-[58%] flex-col flex-nowrap! gap-[7px]">
        <PageHeader :title="t('dispatch.commonContacts')">
          <DispatchTitleIcon icon="bfdx-bianjiyangshi2neibu" @click="openCommonContactsDialog" />
        </PageHeader>
        <DispatchCommonContact
          :items="commonContactsList"
          @locate="handleLocate"
          @call="handleCall"
          @hangup="handleHangup"
          @message="handleMessage"
          @send-command="handleSendCommand"
          @send-message="handleSendMessage"
        />
      </el-row>
      <el-row :gutter="15" class="h-[42%] flex-nowrap!">
        <el-col :span="12" class="flex! flex-col flex-nowrap gap-[7px]">
          <PageHeader :title="t('dispatch.recentContacts')">
            <DispatchTitleIcon icon="bfdx-qingchuyangshi-neibu" @click="cleanRecentContacts" />
          </PageHeader>
          <DispatchRecentContact
            :items="recentContactList"
            @call="handleCall"
            @add-to-common="handleAddToCommonContact"
            @remove-from-common="handleRemoveFromCommonContact"
          />
        </el-col>
        <el-col :span="12" class="flex! flex-col flex-nowrap gap-[7px]">
          <PageHeader :title="t('dispatch.dynamicGroup')">
            <DispatchTitleIcon icon="bfdx-xinzengyangshineibu" @click="openNewDynamicGroupDialog" />
          </PageHeader>
          <DispatchDynamicContact
            :items="dynamicGroupList"
            @locate="handleLocate"
            @call="handleCall"
            @hangup="handleHangup"
            @message="handleMessage"
            @send-command="handleSendCommand"
            @send-message="handleSendMessage"
          />
        </el-col>
      </el-row>
    </el-main>
    <DispatchTree class="w-[350px]! h-full" />
  </el-container>
</template>

<style lang="scss" scoped></style>
