<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import { DispatchContactCardEmit, DispatchContactCardProps } from './DispatchContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { onMounted, onUnmounted, ref } from 'vue'

  const props = withDefaults(
    defineProps<{
      items: Array<DispatchContactCardProps>
    }>(),
    {}
  )

  const emit = defineEmits<DispatchContactCardEmit>()

  const itemSize = ref(calcScaleSize(132))
  const itemSecondarySize = ref(calcScaleSize(228))

  let resizeObserver: ResizeObserver
  const initResizeObserver = () => {
    // 初始化表格行高
    resizeObserver = new ResizeObserver(() => {
      itemSize.value = calcScaleSize(132)
      itemSecondarySize.value = calcScaleSize(228)
    })

    resizeObserver.observe(document.documentElement)
  }

  onMounted(() => {
    initResizeObserver()
  })

  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
  })
</script>

<template>
  <RecycleScroller
    class="contact-container"
    :items="props.items"
    :item-size="itemSize"
    :grid-items="5"
    :item-secondary-size="itemSecondarySize"
    key-field="dmrIDHex"
  >
    <template #default="{ item, index }">
      <DispatchContactCard
        v-if="item.dmrIDHex"
        :key="index"
        v-bind="item"
        @locate="targetDmrId => emit('locate', targetDmrId)"
        @call="targetDmrId => emit('call', targetDmrId)"
        @hangup="targetDmrId => emit('hangup', targetDmrId)"
        @message="targetDmrId => emit('message', targetDmrId)"
        @send-command="targetDmrId => emit('sendCommand', targetDmrId)"
        @send-message="targetDmrId => emit('sendMessage', targetDmrId)"
      />
    </template>
  </RecycleScroller>
</template>

<style lang="scss" scoped>
  .contact-container {
    height: 100%;
    padding: 25px 25px 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));
  }
</style>
