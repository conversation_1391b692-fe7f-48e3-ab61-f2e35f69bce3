<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import { DispatchRecentContactCardEmit, DispatchRecentContactCardProps } from './DispatchRecentContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { onMounted, onUnmounted, ref } from 'vue'

  const props = withDefaults(
    defineProps<{
      items: Array<DispatchRecentContactCardProps>
    }>(),
    {}
  )

  const emit = defineEmits<DispatchRecentContactCardEmit>()

  const itemSize = ref(calcScaleSize(115))
  const itemSecondarySize = ref(calcScaleSize(284))

  let resizeObserver: ResizeObserver
  const initResizeObserver = () => {
    // 初始化表格行高
    resizeObserver = new ResizeObserver(() => {
      itemSize.value = calcScaleSize(115)
      itemSecondarySize.value = calcScaleSize(284)
    })

    resizeObserver.observe(document.documentElement)
  }

  onMounted(() => {
    initResizeObserver()
  })

  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
  })
</script>

<template>
  <RecycleScroller
    class="recent-contact-container"
    :items="props.items"
    :item-size="itemSize"
    :grid-items="2"
    :item-secondary-size="itemSecondarySize"
    key-field="targetDmrIDHex"
  >
    <template #default="{ item, index }">
      <DispatchRecentContactCard
        v-if="item.targetDmrIDHex"
        :key="index"
        v-bind="item"
        @call="targetDmrIDHex => emit('call', targetDmrIDHex)"
        @add-to-common="targetDmrIDHex => emit('addToCommon', targetDmrIDHex)"
        @remove-from-common="targetDmrIDHex => emit('removeFromCommon', targetDmrIDHex)"
      />
    </template>
  </RecycleScroller>
</template>

<style lang="scss" scoped>
  .recent-contact-container {
    height: 100%;
    padding: 32px 5px 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));
  }
</style>
